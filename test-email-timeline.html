<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Timeline Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .header {
      background-color: #10B981;
      padding: 20px;
      text-align: center;
      margin: -20px -20px 20px -20px;
      border-radius: 8px 8px 0 0;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .test-section {
      margin: 30px 0;
      padding: 20px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
    }
    .test-section h3 {
      margin-top: 0;
      color: #1f2937;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>RainbowPaws</h1>
    </div>

    <h2>Email Timeline Test</h2>
    <p>This page tests the email timeline rendering for different booking statuses.</p>

    <div class="test-section">
      <h3>Pending Status</h3>
      <div style="margin: 30px 0; padding: 20px; background-color: #f8fafc; border-radius: 8px; font-family: Arial, sans-serif;">
        <h3 style="margin-top: 0; color: #1f2937; text-align: center; font-size: 18px;">Service Progress</h3>

        <!-- Email-safe table layout for timeline -->
        <table cellpadding="0" cellspacing="0" border="0" style="width: 100%; max-width: 500px; margin: 20px auto;">
          <!-- Progress line row -->
          <tr>
            <td colspan="4" style="height: 40px; padding: 0;">
              <table cellpadding="0" cellspacing="0" border="0" style="width: 100%; margin-top: 20px;">
                <tr>
                  <td style="width: 50px;"></td>
                  <td style="height: 3px; background-color: #e5e7eb;">
                    <table cellpadding="0" cellspacing="0" border="0" style="width: 0%; height: 3px; background-color: #10b981;">
                      <tr><td></td></tr>
                    </table>
                  </td>
                  <td style="width: 50px;"></td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- Steps row -->
          <tr>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #10b981; border: none; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: white; font-weight: bold; font-size: 16px; line-height: 1;">
                    ✓
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #10b981; margin-bottom: 8px; line-height: 1.2;">
                Booking Created
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                Your booking has been submitted
              </div>
            </td>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #ffffff; border: 2px solid #d1d5db; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: #6b7280; font-weight: bold; font-size: 16px; line-height: 1;">
                    ●
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #6b7280; margin-bottom: 8px; line-height: 1.2;">
                Booking Confirmed
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                We have confirmed your booking
              </div>
            </td>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #ffffff; border: 2px solid #d1d5db; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: #6b7280; font-weight: bold; font-size: 16px; line-height: 1;">
                    ●
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #6b7280; margin-bottom: 8px; line-height: 1.2;">
                Service in Progress
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                Your pet is being cared for
              </div>
            </td>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #ffffff; border: 2px solid #d1d5db; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: #6b7280; font-weight: bold; font-size: 16px; line-height: 1;">
                    ●
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #6b7280; margin-bottom: 8px; line-height: 1.2;">
                Service Completed
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                Your service has been completed
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>

    <div class="test-section">
      <h3>Completed Status</h3>
      <div style="margin: 30px 0; padding: 20px; background-color: #f8fafc; border-radius: 8px; font-family: Arial, sans-serif;">
        <h3 style="margin-top: 0; color: #1f2937; text-align: center; font-size: 18px;">Service Progress</h3>

        <!-- Email-safe table layout for timeline -->
        <table cellpadding="0" cellspacing="0" border="0" style="width: 100%; max-width: 500px; margin: 20px auto;">
          <!-- Progress line row -->
          <tr>
            <td colspan="4" style="height: 40px; padding: 0;">
              <table cellpadding="0" cellspacing="0" border="0" style="width: 100%; margin-top: 20px;">
                <tr>
                  <td style="width: 50px;"></td>
                  <td style="height: 3px; background-color: #e5e7eb;">
                    <table cellpadding="0" cellspacing="0" border="0" style="width: 100%; height: 3px; background-color: #10b981;">
                      <tr><td></td></tr>
                    </table>
                  </td>
                  <td style="width: 50px;"></td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- Steps row -->
          <tr>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #10b981; border: none; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: white; font-weight: bold; font-size: 16px; line-height: 1;">
                    ✓
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #10b981; margin-bottom: 8px; line-height: 1.2;">
                Booking Created
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                Your booking has been submitted
              </div>
            </td>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #10b981; border: none; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: white; font-weight: bold; font-size: 16px; line-height: 1;">
                    ✓
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #10b981; margin-bottom: 8px; line-height: 1.2;">
                Booking Confirmed
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                We have confirmed your booking
              </div>
            </td>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #10b981; border: none; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: white; font-weight: bold; font-size: 16px; line-height: 1;">
                    ✓
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #10b981; margin-bottom: 8px; line-height: 1.2;">
                Service in Progress
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                Your pet is being cared for
              </div>
            </td>
            <td style="width: 25%; text-align: center; vertical-align: top; padding: 10px 5px;">
              <!-- Circle using table for better email compatibility -->
              <table cellpadding="0" cellspacing="0" border="0" style="width: 50px; height: 50px; margin: 0 auto 15px; background-color: #10b981; border: none; border-radius: 50px;">
                <tr>
                  <td style="text-align: center; vertical-align: middle; color: white; font-weight: bold; font-size: 16px; line-height: 1;">
                    ✓
                  </td>
                </tr>
              </table>

              <!-- Text -->
              <div style="font-size: 14px; font-weight: 600; color: #10b981; margin-bottom: 8px; line-height: 1.2;">
                Service Completed
              </div>
              <div style="font-size: 12px; color: #6b7280; line-height: 1.3;">
                Your service has been completed
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>

  </div>
</body>
</html>
